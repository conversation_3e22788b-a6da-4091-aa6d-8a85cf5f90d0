#!/bin/bash

# JMeter Bot - Simple Wrapper Script
# Converts your JMeter .jmx files into easy-to-run bot tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
THREADS=10
DURATION=60
RAMP_TIME=30
JMX_FILE=""
TARGET_URL=""
RESULTS_DIR="./bot-results"

# Bot banner
show_banner() {
    echo -e "${BLUE}"
    echo "  ╔══════════════════════════════════════╗"
    echo "  ║          🤖 JMeter Bot Runner        ║"
    echo "  ║     Easy Social Media API Testing    ║"
    echo "  ╚══════════════════════════════════════╝"
    echo -e "${NC}"
}

# Help function
show_help() {
    echo -e "${YELLOW}Usage:${NC}"
    echo "  $0 [OPTIONS]"
    echo ""
    echo -e "${YELLOW}Options:${NC}"
    echo "  -j, --jmx FILE        JMX test plan file"
    echo "  -u, --url URL         Target URL (creates simple test)"
    echo "  -t, --threads NUM     Number of virtual users (default: 10)"
    echo "  -d, --duration SEC    Test duration in seconds (default: 60)"
    echo "  -r, --ramp SEC        Ramp-up time in seconds (default: 30)"
    echo "  -o, --output DIR      Results output directory (default: ./bot-results)"
    echo "  -h, --help            Show this help"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo "  # Run with existing JMX file"
    echo "  $0 -j my_test.jmx -t 50 -d 300"
    echo ""
    echo "  # Create simple URL test"
    echo "  $0 -u https://api.example.com -t 20 -d 120"
    echo ""
    echo "  # Heavy load test"
    echo "  $0 -j stress_test.jmx -t 200 -d 600"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -j|--jmx)
                JMX_FILE="$2"
                shift 2
                ;;
            -u|--url)
                TARGET_URL="$2"
                shift 2
                ;;
            -t|--threads)
                THREADS="$2"
                shift 2
                ;;
            -d|--duration)
                DURATION="$2"
                shift 2
                ;;
            -r|--ramp)
                RAMP_TIME="$2"
                shift 2
                ;;
            -o|--output)
                RESULTS_DIR="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo -e "${RED}❌ Unknown option: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
}

# Check if JMeter is installed
check_jmeter() {
    if ! command -v jmeter &> /dev/null; then
        echo -e "${RED}❌ JMeter is not installed or not in PATH${NC}"
        echo -e "${YELLOW}💡 Please install JMeter:${NC}"
        echo "   - Download from: https://jmeter.apache.org/download_jmeter.cgi"
        echo "   - Add to PATH: export PATH=\$PATH:/path/to/jmeter/bin"
        exit 1
    fi
    
    echo -e "${GREEN}✅ JMeter found: $(jmeter --version | head -n1)${NC}"
}

# Create simple JMX file for URL testing
create_simple_jmx() {
    local url="$1"
    local output_file="simple_bot_test.jmx"
    
    # Extract domain and path from URL
    local protocol=$(echo "$url" | sed -n 's|^\([^:]*\)://.*|\1|p')
    local domain=$(echo "$url" | sed -n 's|^[^:]*://\([^/]*\).*|\1|p')
    local path=$(echo "$url" | sed -n 's|^[^:]*://[^/]*\(.*\)|\1|p')
    
    if [[ -z "$path" ]]; then
        path="/"
    fi
    
cat > "$output_file" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.4.1">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="Bot Test Plan" enabled="true">
      <stringProp name="TestPlan.comments">Generated by JMeter Bot</stringProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Bot Virtual Users" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControllerGui" testclass="LoopController" testname="Loop Controller" enabled="true">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <intProp name="LoopController.loops">-1</intProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">\${__P(threads,$THREADS)}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">\${__P(ramp_time,$RAMP_TIME)}</stringProp>
        <boolProp name="ThreadGroup.scheduler">true</boolProp>
        <stringProp name="ThreadGroup.duration">\${__P(duration,$DURATION)}</stringProp>
        <stringProp name="ThreadGroup.delay"></stringProp>
      </ThreadGroup>
      <hashTree>
        <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="API Bot Request" enabled="true">
          <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
            <collectionProp name="Arguments.arguments"/>
          </elementProp>
          <stringProp name="HTTPSampler.domain">$domain</stringProp>
          <stringProp name="HTTPSampler.port"></stringProp>
          <stringProp name="HTTPSampler.protocol">$protocol</stringProp>
          <stringProp name="HTTPSampler.contentEncoding"></stringProp>
          <stringProp name="HTTPSampler.path">$path</stringProp>
          <stringProp name="HTTPSampler.method">GET</stringProp>
          <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
          <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
          <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
        </HTTPSamplerProxy>
        <hashTree/>
        
        <!-- Random delay between requests -->
        <UniformRandomTimer guiclass="UniformRandomTimerGui" testclass="UniformRandomTimer" testname="Random Delay" enabled="true">
          <stringProp name="ConstantTimer.delay">1000</stringProp>
          <stringProp name="RandomTimer.range">2000</stringProp>
        </UniformRandomTimer>
        <hashTree/>
      </hashTree>
      
      <!-- Results listeners -->
      <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree" enabled="false">
        <boolProp name="ResultCollector.error_logging">false</boolProp>
        <objProp>
          <name>saveConfig</name>
          <value class="SampleSaveConfiguration">
            <time>true</time>
            <latency>true</latency>
            <timestamp>true</timestamp>
            <success>true</success>
            <label>true</label>
            <code>true</code>
            <message>true</message>
            <threadName>true</threadName>
            <dataType>true</dataType>
            <encoding>false</encoding>
            <assertions>true</assertions>
            <subresults>true</subresults>
            <responseData>false</responseData>
            <samplerData>false</samplerData>
            <xml>false</xml>
            <fieldNames>true</fieldNames>
            <responseHeaders>false</responseHeaders>
            <requestHeaders>false</requestHeaders>
            <responseDataOnError>false</responseDataOnError>
            <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
            <assertionsResultsToSave>0</assertionsResultsToSave>
            <bytes>true</bytes>
            <sentBytes>true</sentBytes>
            <url>true</url>
            <threadCounts>true</threadCounts>
            <idleTime>true</idleTime>
            <connectTime>true</connectTime>
          </value>
        </objProp>
        <stringProp name="filename"></stringProp>
      </ResultCollector>
      <hashTree/>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
EOF

    echo "$output_file"
}

# Run the JMeter test
run_test() {
    local jmx_file="$1"
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local results_file="$RESULTS_DIR/bot_results_$timestamp.jtl"
    local report_dir="$RESULTS_DIR/bot_report_$timestamp"
    
    # Create results directory
    mkdir -p "$RESULTS_DIR"
    
    echo -e "${BLUE}🤖 Starting bot test...${NC}"
    echo -e "${YELLOW}📊 Configuration:${NC}"
    echo "   Virtual Users: $THREADS"
    echo "   Duration: $DURATION seconds"
    echo "   Ramp-up: $RAMP_TIME seconds"
    echo "   JMX File: $jmx_file"
    echo ""
    
    # Build JMeter command
    local jmeter_cmd=(
        jmeter
        -n
        -t "$jmx_file"
        -l "$results_file"
        -e
        -o "$report_dir"
        -Jthreads="$THREADS"
        -Jduration="$DURATION"
        -Jramp_time="$RAMP_TIME"
    )
    
    echo -e "${BLUE}🚀 Executing: ${jmeter_cmd[*]}${NC}"
    echo ""
    
    # Run the test
    if "${jmeter_cmd[@]}"; then
        echo ""
        echo -e "${GREEN}✅ Bot test completed successfully!${NC}"
        echo -e "${YELLOW}📁 Results saved to:${NC}"
        echo "   Raw data: $results_file"
        echo "   HTML report: $report_dir/index.html"
        echo ""
        
        # Show basic stats if available
        if [[ -f "$results_file" ]]; then
            show_quick_stats "$results_file"
        fi
        
        # Open report if on macOS/Linux with GUI
        if command -v open &> /dev/null; then
            echo -e "${BLUE}🌐 Opening report in browser...${NC}"
            open "$report_dir/index.html"
        elif command -v xdg-open &> /dev/null; then
            echo -e "${BLUE}🌐 Opening report in browser...${NC}"
            xdg-open "$report_dir/index.html"
        fi
        
    else
        echo -e "${RED}❌ Bot test failed!${NC}"
        echo "Check the JMeter logs above for details."
        exit 1
    fi
}

# Show quick statistics
show_quick_stats() {
    local results_file="$1"
    
    if [[ ! -f "$results_file" ]]; then
        return
    fi
    
    echo -e "${YELLOW}📊 Quick Stats:${NC}"
    
    # Count total samples
    local total_samples=$(tail -n +2 "$results_file" | wc -l)
    echo "   Total Requests: $total_samples"
    
    # Count successful samples
    local success_samples=$(tail -n +2 "$results_file" | awk -F',' '$8=="true" {count++} END {print count+0}')
    echo "   Successful: $success_samples"
    
    # Calculate success rate
    if [[ $total_samples -gt 0 ]]; then
        local success_rate=$(echo "scale=1; $success_samples * 100 / $total_samples" | bc -l 2>/dev/null || echo "N/A")
        echo "   Success Rate: $success_rate%"
    fi
    
    # Average response time
    local avg_response=$(tail -n +2 "$results_file" | awk -F',' '{sum+=$2; count++} END {if(count>0) print int(sum/count); else print "N/A"}')
    echo "   Avg Response Time: ${avg_response}ms"
    
    echo ""
}

# Main function
main() {
    show_banner
    
    # Parse arguments
    parse_args "$@"
    
    # Check if we have either JMX file or URL
    if [[ -z "$JMX_FILE" && -z "$TARGET_URL" ]]; then
        echo -e "${RED}❌ Please provide either a JMX file (-j) or target URL (-u)${NC}"
        show_help
        exit 1
    fi
    
    # Check JMeter installation
    check_jmeter
    
    # Determine which JMX file to use
    local jmx_to_use=""
    
    if [[ -n "$TARGET_URL" ]]; then
        echo -e "${BLUE}🔧 Creating simple test for URL: $TARGET_URL${NC}"
        jmx_to_use=$(create_simple_jmx "$TARGET_URL")
        echo -e "${GREEN}✅ Generated JMX file: $jmx_to_use${NC}"
    elif [[ -n "$JMX_FILE" ]]; then
        if [[ ! -f "$JMX_FILE" ]]; then
            echo -e "${RED}❌ JMX file not found: $JMX_FILE${NC}"
            exit 1
        fi
        jmx_to_use="$JMX_FILE"
    fi
    
    # Run the test
    run_test "$jmx_to_use"
    
    # Cleanup generated files if we created them
    if [[ -n "$TARGET_URL" && -f "simple_bot_test.jmx" ]]; then
        echo -e "${BLUE}🧹 Cleaning up temporary files...${NC}"
        # Optionally remove the generated JMX file
        # rm "simple_bot_test.jmx"
    fi
    
    echo -e "${GREEN}🎉 JMeter Bot test completed!${NC}"

    # Keep terminal open
    echo ""
    echo -e "${YELLOW}Press any key to close the terminal...${NC}"
    read -n 1 -s
}

# Run main function with all arguments
main "$@"