<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.6.2">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="Chakra Stress Test" enabled="true">
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.tearDown_on_shutdown">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Thread Group" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller" enabled="true">
          <stringProp name="LoopController.loops">1</stringProp>
          <boolProp name="LoopController.continue_forever">false</boolProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">500</stringProp>
        <stringProp name="ThreadGroup.ramp_time">1</stringProp>
        <boolProp name="ThreadGroup.delayedStart">false</boolProp>
        <boolProp name="ThreadGroup.scheduler">false</boolProp>
        <stringProp name="ThreadGroup.duration"></stringProp>
        <stringProp name="ThreadGroup.delay"></stringProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
      </ThreadGroup>
      <hashTree>
        <UserParameters guiclass="UserParametersGui" testclass="UserParameters" testname="Request Parameters" enabled="true">
          <collectionProp name="UserParameters.names">
            <stringProp name="-989163880">protocol</stringProp>
            <stringProp name="3208616">host</stringProp>
            <stringProp name="3446913">port</stringProp>
            <stringProp name="-1553190589">deviceToken</stringProp>
            <stringProp name="-1042689291">accessToken</stringProp>
            <stringProp name="-926469262">backup_endpoint</stringProp>
          </collectionProp>
          <collectionProp name="UserParameters.thread_values">
            <collectionProp name="2072882346">
              <stringProp name="3213448">http</stringProp>
              <stringProp name="-1087224444">************</stringProp>
              <stringProp name="1716958">8116</stringProp>
              <stringProp name="-157075157">eOn_IQIhThy1N4povG424j:APA91bFdWhJlhha8JtWBEBWa6FMUeRsGAvwwua4C96Qau0CrTeoYl55I_9_DuCftrprZqxTkMocqvfpozBmlp15tpCj7m9Ulbf4W3xnHQYb1F6S4_QcL3Xw</stringProp>
              <stringProp name="1857086553">ef7n2lbokhufacvk0qoeorpm2tn12eci</stringProp>
              <stringProp name="1673369937">https://chakra.test.ecoinsoft.com:443</stringProp>
            </collectionProp>
          </collectionProp>
          <boolProp name="UserParameters.per_iteration">false</boolProp>
        </UserParameters>
        <hashTree/>
        <UserParameters guiclass="UserParametersGui" testclass="UserParameters" testname="Content Parameters" enabled="true">
          <collectionProp name="UserParameters.names">
            <stringProp name="-1019779949">offset</stringProp>
            <stringProp name="107876">max</stringProp>
            <stringProp name="-725711140">totalItems</stringProp>
            <stringProp name="-753878037">continueLoop</stringProp>
          </collectionProp>
          <collectionProp name="UserParameters.thread_values">
            <collectionProp name="-1888845809">
              <stringProp name="48">0</stringProp>
              <stringProp name="1567">10</stringProp>
              <stringProp name="48">0</stringProp>
              <stringProp name="3569038">true</stringProp>
            </collectionProp>
          </collectionProp>
          <boolProp name="UserParameters.per_iteration">false</boolProp>
        </UserParameters>
        <hashTree/>
        <HeaderManager guiclass="HeaderPanel" testclass="HeaderManager" testname="HTTP Header Manager" enabled="true">
          <collectionProp name="HeaderManager.headers">
            <elementProp name="Authorization" elementType="Header">
              <stringProp name="Header.name">Authorization</stringProp>
              <stringProp name="Header.value">Bearer ${accessToken}</stringProp>
            </elementProp>
            <elementProp name="X-API-KEY" elementType="Header">
              <stringProp name="Header.name">X-API-KEY</stringProp>
              <stringProp name="Header.value">1ccbc4c913bc4ce785a0a2de444aa0d6</stringProp>
            </elementProp>
            <elementProp name="deviceToken" elementType="Header">
              <stringProp name="Header.name">deviceToken</stringProp>
              <stringProp name="Header.value">${deviceToken}</stringProp>
            </elementProp>
            <elementProp name="deviceType" elementType="Header">
              <stringProp name="Header.name">deviceType</stringProp>
              <stringProp name="Header.value">ANDROID</stringProp>
            </elementProp>
            <elementProp name="deviceId" elementType="Header">
              <stringProp name="Header.name">deviceId</stringProp>
              <stringProp name="Header.value">SKQ1.211113.001</stringProp>
            </elementProp>
            <elementProp name="deviceModel" elementType="Header">
              <stringProp name="Header.name">deviceModel</stringProp>
              <stringProp name="Header.value">SamSung S24 Ultra</stringProp>
            </elementProp>
            <elementProp name="Content-Type" elementType="Header">
              <stringProp name="Header.name">Content-Type</stringProp>
              <stringProp name="Header.value">application/json</stringProp>
            </elementProp>
          </collectionProp>
        </HeaderManager>
        <hashTree/>
        <GenericController guiclass="LogicControllerGui" testclass="GenericController" testname="Simple Controller (Home Screen)" enabled="true"/>
        <hashTree>
          <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Get user profile" enabled="true">
            <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
            <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
              <collectionProp name="Arguments.arguments"/>
            </elementProp>
            <stringProp name="HTTPSampler.domain">${host}</stringProp>
            <stringProp name="HTTPSampler.port">${port}</stringProp>
            <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
            <stringProp name="HTTPSampler.path">/api/userAccounts/me</stringProp>
            <stringProp name="HTTPSampler.method">GET</stringProp>
            <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
            <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
            <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
            <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
            <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
            <boolProp name="HTTPSampler.image_parser">false</boolProp>
            <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
            <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
            <boolProp name="HTTPSampler.md5">false</boolProp>
            <intProp name="HTTPSampler.ipSourceType">0</intProp>
          </HTTPSamplerProxy>
          <hashTree>
            <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON Extractor (Get username)" enabled="true">
              <stringProp name="JSONPostProcessor.referenceNames">username</stringProp>
              <stringProp name="JSONPostProcessor.jsonPathExprs">$.data.username</stringProp>
              <stringProp name="JSONPostProcessor.match_numbers">1</stringProp>
              <stringProp name="JSONPostProcessor.defaultValues">admin</stringProp>
            </JSONPostProcessor>
            <hashTree/>
          </hashTree>
          <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Get badge count" enabled="true">
            <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
            <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
              <collectionProp name="Arguments.arguments">
                <elementProp name="chanelName" elementType="HTTPArgument">
                  <boolProp name="HTTPArgument.always_encode">false</boolProp>
                  <stringProp name="Argument.metadata">=</stringProp>
                  <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  <stringProp name="Argument.name">chanelName</stringProp>
                  <stringProp name="Argument.value">${deviceToken}</stringProp>
                </elementProp>
              </collectionProp>
            </elementProp>
            <stringProp name="HTTPSampler.domain">${host}</stringProp>
            <stringProp name="HTTPSampler.port">${port}</stringProp>
            <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
            <stringProp name="HTTPSampler.path">/api/notification/getBadgeCount</stringProp>
            <stringProp name="HTTPSampler.method">GET</stringProp>
            <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
            <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
            <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
            <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
            <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
            <boolProp name="HTTPSampler.image_parser">false</boolProp>
            <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
            <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
            <boolProp name="HTTPSampler.md5">false</boolProp>
            <intProp name="HTTPSampler.ipSourceType">0</intProp>
          </HTTPSamplerProxy>
          <hashTree/>
          <OnceOnlyController guiclass="OnceOnlyControllerGui" testclass="OnceOnlyController" testname="Once Only Controller (Update user device)" enabled="true"/>
          <hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Update user device" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.value">{&#xd;
	&quot;username&quot; : &quot;${username}&quot; &#xd;
}</stringProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${host}</stringProp>
              <stringProp name="HTTPSampler.port">${port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
              <stringProp name="HTTPSampler.path">/api/userDevice/updateUserDevice</stringProp>
              <stringProp name="HTTPSampler.method">PUT</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">false</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">false</boolProp>
              <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
              <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
              <boolProp name="HTTPSampler.md5">false</boolProp>
              <intProp name="HTTPSampler.ipSourceType">0</intProp>
            </HTTPSamplerProxy>
            <hashTree/>
          </hashTree>
          <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List user story" enabled="true">
            <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
            <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
              <collectionProp name="Arguments.arguments">
                <elementProp name="offset" elementType="HTTPArgument">
                  <boolProp name="HTTPArgument.always_encode">false</boolProp>
                  <stringProp name="Argument.metadata">=</stringProp>
                  <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  <stringProp name="Argument.name">offset</stringProp>
                  <stringProp name="Argument.value">0</stringProp>
                  <stringProp name="HTTPArgument.content_type">text/plain</stringProp>
                </elementProp>
                <elementProp name="max" elementType="HTTPArgument">
                  <boolProp name="HTTPArgument.always_encode">false</boolProp>
                  <stringProp name="Argument.metadata">=</stringProp>
                  <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  <stringProp name="Argument.name">max</stringProp>
                  <stringProp name="Argument.value">10</stringProp>
                </elementProp>
              </collectionProp>
            </elementProp>
            <stringProp name="HTTPSampler.domain">${host}</stringProp>
            <stringProp name="HTTPSampler.port">${port}</stringProp>
            <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
            <stringProp name="HTTPSampler.path">/api/userStory</stringProp>
            <stringProp name="HTTPSampler.method">GET</stringProp>
            <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
            <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
            <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
            <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
            <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
            <boolProp name="HTTPSampler.image_parser">false</boolProp>
            <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
            <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
            <boolProp name="HTTPSampler.md5">false</boolProp>
            <intProp name="HTTPSampler.ipSourceType">0</intProp>
          </HTTPSamplerProxy>
          <hashTree/>
          <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List home contents" enabled="true">
            <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
            <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
              <collectionProp name="Arguments.arguments">
                <elementProp name="" elementType="HTTPArgument">
                  <boolProp name="HTTPArgument.always_encode">false</boolProp>
                  <stringProp name="Argument.metadata">=</stringProp>
                  <boolProp name="HTTPArgument.use_equals">true</boolProp>
                </elementProp>
                <elementProp name="offset" elementType="HTTPArgument">
                  <boolProp name="HTTPArgument.always_encode">false</boolProp>
                  <stringProp name="Argument.metadata">=</stringProp>
                  <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  <stringProp name="Argument.name">offset</stringProp>
                  <stringProp name="Argument.value">${offset}</stringProp>
                </elementProp>
                <elementProp name="max" elementType="HTTPArgument">
                  <boolProp name="HTTPArgument.always_encode">false</boolProp>
                  <stringProp name="Argument.metadata">=</stringProp>
                  <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  <stringProp name="Argument.name">max</stringProp>
                  <stringProp name="Argument.value">${max}</stringProp>
                </elementProp>
              </collectionProp>
            </elementProp>
            <stringProp name="HTTPSampler.domain">${host}</stringProp>
            <stringProp name="HTTPSampler.port">${port}</stringProp>
            <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
            <stringProp name="HTTPSampler.path">/api/contents</stringProp>
            <stringProp name="HTTPSampler.method">GET</stringProp>
            <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
            <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
            <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
            <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
            <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
            <boolProp name="HTTPSampler.image_parser">false</boolProp>
            <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
            <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
            <boolProp name="HTTPSampler.md5">false</boolProp>
            <intProp name="HTTPSampler.ipSourceType">0</intProp>
          </HTTPSamplerProxy>
          <hashTree>
            <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON Extractor (List of content ID)" enabled="true">
              <stringProp name="JSONPostProcessor.referenceNames">contentIdList</stringProp>
              <stringProp name="JSONPostProcessor.jsonPathExprs">$.data[*].id</stringProp>
              <stringProp name="JSONPostProcessor.match_numbers">-1</stringProp>
              <stringProp name="Scope.variable">contentList</stringProp>
            </JSONPostProcessor>
            <hashTree/>
            <JSR223PostProcessor guiclass="TestBeanGUI" testclass="JSR223PostProcessor" testname="JSR223 PostProcessor (CommaSeparatedContentIds)" enabled="true">
              <stringProp name="cacheKey">true</stringProp>
              <stringProp name="filename"></stringProp>
              <stringProp name="parameters"></stringProp>
              <stringProp name="script">StringBuilder ids = new StringBuilder();
int matchNr = vars.get(&quot;contentIdList_matchNr&quot;) as int;

for (int i = 1; i &lt;= matchNr; i++) {
    ids.append(vars.get(&quot;contentIdList_&quot; + i));
    if (i &lt; matchNr) {
        ids.append(&quot;,&quot;);
    }
}

vars.put(&quot;commaSeparatedContentIds&quot;, ids.toString());</stringProp>
              <stringProp name="scriptLanguage">groovy</stringProp>
            </JSR223PostProcessor>
            <hashTree/>
            <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON Extractor (Total items count)" enabled="true">
              <stringProp name="JSONPostProcessor.referenceNames">totalItems</stringProp>
              <stringProp name="JSONPostProcessor.jsonPathExprs">$.total</stringProp>
              <stringProp name="JSONPostProcessor.match_numbers">1</stringProp>
            </JSONPostProcessor>
            <hashTree/>
          </hashTree>
          <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List content engagements" enabled="true">
            <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
            <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
              <collectionProp name="Arguments.arguments">
                <elementProp name="" elementType="HTTPArgument">
                  <boolProp name="HTTPArgument.always_encode">false</boolProp>
                  <stringProp name="Argument.metadata">=</stringProp>
                  <boolProp name="HTTPArgument.use_equals">true</boolProp>
                </elementProp>
                <elementProp name="contentIds" elementType="HTTPArgument">
                  <boolProp name="HTTPArgument.always_encode">false</boolProp>
                  <stringProp name="Argument.metadata">=</stringProp>
                  <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  <stringProp name="Argument.name">contentIds</stringProp>
                  <stringProp name="Argument.value">${commaSeparatedContentIds}</stringProp>
                </elementProp>
              </collectionProp>
            </elementProp>
            <stringProp name="HTTPSampler.domain">${host}</stringProp>
            <stringProp name="HTTPSampler.port">${port}</stringProp>
            <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
            <stringProp name="HTTPSampler.path">/api/contents/engagementSummary</stringProp>
            <stringProp name="HTTPSampler.method">GET</stringProp>
            <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
            <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
            <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
            <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
            <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
            <boolProp name="HTTPSampler.image_parser">false</boolProp>
            <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
            <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
            <boolProp name="HTTPSampler.md5">false</boolProp>
            <intProp name="HTTPSampler.ipSourceType">0</intProp>
          </HTTPSamplerProxy>
          <hashTree/>
          <WhileController guiclass="WhileControllerGui" testclass="WhileController" testname="While Controller (for subsequent pages)" enabled="false">
            <stringProp name="WhileController.condition">${continueLoop}</stringProp>
            <stringProp name="TestPlan.comments">Not working for now as our API response total not correctly</stringProp>
          </WhileController>
          <hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List home contents" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="offset" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    <stringProp name="Argument.name">offset</stringProp>
                    <stringProp name="Argument.value">${offset}</stringProp>
                  </elementProp>
                  <elementProp name="max" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    <stringProp name="Argument.name">max</stringProp>
                    <stringProp name="Argument.value">${max}</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${host}</stringProp>
              <stringProp name="HTTPSampler.port">${port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
              <stringProp name="HTTPSampler.path">/api/contents</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">false</boolProp>
              <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
              <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
              <boolProp name="HTTPSampler.md5">false</boolProp>
              <intProp name="HTTPSampler.ipSourceType">0</intProp>
            </HTTPSamplerProxy>
            <hashTree>
              <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON Extractor (List of content ID)" enabled="true">
                <stringProp name="JSONPostProcessor.referenceNames">contentIdList</stringProp>
                <stringProp name="JSONPostProcessor.jsonPathExprs">$.data[*].id</stringProp>
                <stringProp name="JSONPostProcessor.match_numbers">-1</stringProp>
                <stringProp name="Scope.variable">contentList</stringProp>
              </JSONPostProcessor>
              <hashTree/>
              <JSR223PostProcessor guiclass="TestBeanGUI" testclass="JSR223PostProcessor" testname="JSR223 PostProcessor (CommaSeparatedContentIds)" enabled="true">
                <stringProp name="cacheKey">true</stringProp>
                <stringProp name="filename"></stringProp>
                <stringProp name="parameters"></stringProp>
                <stringProp name="script">StringBuilder ids = new StringBuilder();
int matchNr = vars.get(&quot;contentIdList_matchNr&quot;) as int;

for (int i = 1; i &lt;= matchNr; i++) {
    ids.append(vars.get(&quot;contentIdList_&quot; + i));
    if (i &lt; matchNr) {
        ids.append(&quot;,&quot;);
    }
}

vars.put(&quot;commaSeparatedContentIds&quot;, ids.toString());</stringProp>
                <stringProp name="scriptLanguage">groovy</stringProp>
              </JSR223PostProcessor>
              <hashTree/>
              <JSR223PostProcessor guiclass="TestBeanGUI" testclass="JSR223PostProcessor" testname="JSR223 PostProcessor" enabled="true">
                <stringProp name="scriptLanguage">groovy</stringProp>
                <stringProp name="parameters"></stringProp>
                <stringProp name="filename"></stringProp>
                <stringProp name="cacheKey">true</stringProp>
                <stringProp name="script">// Get current offset, max page size, and total items count
int currentOffset = vars.get(&quot;offset&quot;) as int;
int maxItems = vars.get(&quot;max&quot;) as int;
int total = vars.get(&quot;totalItems&quot;) as int;

// Calculate the offset for the next page
int nextOffset = currentOffset + maxItems;

// Update the &apos;offset&apos; variable for the next iteration of the loop
vars.put(&quot;offset&quot;, String.valueOf(nextOffset));

// Determine if the loop should continue
// Continue if the calculated next offset is less than the total number of items
if (nextOffset &lt; total) {
    vars.put(&quot;continueLoop&quot;, &quot;true&quot;);
} else {
    vars.put(&quot;continueLoop&quot;, &quot;false&quot;);
}

// Optional: Log the current offset and loop status for debugging
// log.info(&quot;Current Offset: &quot; + currentOffset + &quot;, Next Offset: &quot; + nextOffset + &quot;, Total: &quot; + total + &quot;, Continue Loop: &quot; + vars.get(&quot;continueLoop&quot;));</stringProp>
              </JSR223PostProcessor>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List content engagements" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="contentIds" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    <stringProp name="Argument.name">contentIds</stringProp>
                    <stringProp name="Argument.value">${commaSeparatedContentIds}</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${host}</stringProp>
              <stringProp name="HTTPSampler.port">${port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
              <stringProp name="HTTPSampler.path">/api/contents/engagementSummary</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">false</boolProp>
              <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
              <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
              <boolProp name="HTTPSampler.md5">false</boolProp>
              <intProp name="HTTPSampler.ipSourceType">0</intProp>
            </HTTPSamplerProxy>
            <hashTree/>
            <UniformRandomTimer guiclass="UniformRandomTimerGui" testclass="UniformRandomTimer" testname="Uniform Random Timer (1s-2s)" enabled="true">
              <stringProp name="ConstantTimer.delay">1000</stringProp>
              <stringProp name="RandomTimer.range">2000</stringProp>
            </UniformRandomTimer>
            <hashTree/>
          </hashTree>
          <LoopController guiclass="LoopControlPanel" testclass="LoopController" testname="Loop Controller (Content Scrolling)" enabled="true">
            <stringProp name="LoopController.loops">5</stringProp>
          </LoopController>
          <hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List home contents" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="offset" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    <stringProp name="Argument.name">offset</stringProp>
                    <stringProp name="Argument.value">${offset}</stringProp>
                  </elementProp>
                  <elementProp name="max" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    <stringProp name="Argument.name">max</stringProp>
                    <stringProp name="Argument.value">${max}</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${host}</stringProp>
              <stringProp name="HTTPSampler.port">${port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
              <stringProp name="HTTPSampler.path">/api/contents</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">false</boolProp>
              <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
              <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
              <boolProp name="HTTPSampler.md5">false</boolProp>
              <intProp name="HTTPSampler.ipSourceType">0</intProp>
            </HTTPSamplerProxy>
            <hashTree>
              <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON Extractor (List of content ID)" enabled="true">
                <stringProp name="JSONPostProcessor.referenceNames">contentIdList</stringProp>
                <stringProp name="JSONPostProcessor.jsonPathExprs">$.data[*].id</stringProp>
                <stringProp name="JSONPostProcessor.match_numbers">-1</stringProp>
                <stringProp name="Scope.variable">contentList</stringProp>
              </JSONPostProcessor>
              <hashTree/>
              <JSR223PostProcessor guiclass="TestBeanGUI" testclass="JSR223PostProcessor" testname="JSR223 PostProcessor (CommaSeparatedContentIds)" enabled="true">
                <stringProp name="cacheKey">true</stringProp>
                <stringProp name="filename"></stringProp>
                <stringProp name="parameters"></stringProp>
                <stringProp name="script">StringBuilder ids = new StringBuilder();
int matchNr = vars.get(&quot;contentIdList_matchNr&quot;) as int;

for (int i = 1; i &lt;= matchNr; i++) {
    ids.append(vars.get(&quot;contentIdList_&quot; + i));
    if (i &lt; matchNr) {
        ids.append(&quot;,&quot;);
    }
}

vars.put(&quot;commaSeparatedContentIds&quot;, ids.toString());</stringProp>
                <stringProp name="scriptLanguage">groovy</stringProp>
              </JSR223PostProcessor>
              <hashTree/>
            </hashTree>
            <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List content engagements" enabled="true">
              <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
              <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                <collectionProp name="Arguments.arguments">
                  <elementProp name="" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                  </elementProp>
                  <elementProp name="contentIds" elementType="HTTPArgument">
                    <boolProp name="HTTPArgument.always_encode">false</boolProp>
                    <stringProp name="Argument.metadata">=</stringProp>
                    <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    <stringProp name="Argument.name">contentIds</stringProp>
                    <stringProp name="Argument.value">${commaSeparatedContentIds}</stringProp>
                  </elementProp>
                </collectionProp>
              </elementProp>
              <stringProp name="HTTPSampler.domain">${host}</stringProp>
              <stringProp name="HTTPSampler.port">${port}</stringProp>
              <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
              <stringProp name="HTTPSampler.path">/api/contents/engagementSummary</stringProp>
              <stringProp name="HTTPSampler.method">GET</stringProp>
              <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
              <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
              <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
              <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
              <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
              <boolProp name="HTTPSampler.image_parser">false</boolProp>
              <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
              <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
              <boolProp name="HTTPSampler.md5">false</boolProp>
              <intProp name="HTTPSampler.ipSourceType">0</intProp>
            </HTTPSamplerProxy>
            <hashTree/>
            <UniformRandomTimer guiclass="UniformRandomTimerGui" testclass="UniformRandomTimer" testname="Uniform Random Timer (1s-2s)" enabled="true">
              <stringProp name="ConstantTimer.delay">1000</stringProp>
              <stringProp name="RandomTimer.range">2000</stringProp>
            </UniformRandomTimer>
            <hashTree/>
            <RandomController guiclass="RandomControlGui" testclass="RandomController" testname="Random Controller (Like, Comment, and Share)" enabled="true">
              <intProp name="InterleaveControl.style">1</intProp>
            </RandomController>
            <hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Like a content" enabled="true">
                <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                    <elementProp name="contentId" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.name">contentId</stringProp>
                      <stringProp name="Argument.value">${contentIdList_1}</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain">${host}</stringProp>
                <stringProp name="HTTPSampler.port">${port}</stringProp>
                <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                <stringProp name="HTTPSampler.path">/api/likeHistories</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                <boolProp name="HTTPSampler.image_parser">false</boolProp>
                <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                <boolProp name="HTTPSampler.md5">false</boolProp>
                <intProp name="HTTPSampler.ipSourceType">0</intProp>
              </HTTPSamplerProxy>
              <hashTree/>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List comment of a content" enabled="true">
                <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                    </elementProp>
                    <elementProp name="contentId" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.name">contentId</stringProp>
                      <stringProp name="Argument.value">${contentIdList_1}</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain">${host}</stringProp>
                <stringProp name="HTTPSampler.port">${port}</stringProp>
                <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                <stringProp name="HTTPSampler.path">/api/contentComments</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                <boolProp name="HTTPSampler.image_parser">false</boolProp>
                <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                <boolProp name="HTTPSampler.md5">false</boolProp>
                <intProp name="HTTPSampler.ipSourceType">0</intProp>
              </HTTPSamplerProxy>
              <hashTree/>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Share a content" enabled="true">
                <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.value">{&#xd;
    &quot;contentId&quot;: ${contentIdList_1},&#xd;
    &quot;circleId&quot;: null&#xd;
}</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain">${host}</stringProp>
                <stringProp name="HTTPSampler.port">${port}</stringProp>
                <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                <stringProp name="HTTPSampler.path">/api/shareHistories</stringProp>
                <stringProp name="HTTPSampler.method">POST</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                <boolProp name="HTTPSampler.image_parser">false</boolProp>
                <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                <boolProp name="HTTPSampler.md5">false</boolProp>
                <intProp name="HTTPSampler.ipSourceType">0</intProp>
              </HTTPSamplerProxy>
              <hashTree/>
              <OnceOnlyController guiclass="OnceOnlyControllerGui" testclass="OnceOnlyController" testname="Once Only Controller" enabled="true"/>
              <hashTree>
                <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="Constant Timer (3s to write a comment)" enabled="true">
                  <stringProp name="ConstantTimer.delay">3000</stringProp>
                </ConstantTimer>
                <hashTree/>
                <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Comment on a content" enabled="true">
                  <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
                  <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                    <collectionProp name="Arguments.arguments">
                      <elementProp name="" elementType="HTTPArgument">
                        <boolProp name="HTTPArgument.always_encode">false</boolProp>
                        <stringProp name="Argument.value">{&#xd;
    &quot;contentId&quot;: ${contentIdList_1},&#xd;
    &quot;parentId&quot;: null,&#xd;
    &quot;comment&quot;: &quot;So good!&quot;&#xd;
}</stringProp>
                        <stringProp name="Argument.metadata">=</stringProp>
                      </elementProp>
                    </collectionProp>
                  </elementProp>
                  <stringProp name="HTTPSampler.domain">${host}</stringProp>
                  <stringProp name="HTTPSampler.port">${port}</stringProp>
                  <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                  <stringProp name="HTTPSampler.path">/api/contentComments</stringProp>
                  <stringProp name="HTTPSampler.method">POST</stringProp>
                  <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                  <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                  <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                  <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                  <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                  <boolProp name="HTTPSampler.image_parser">false</boolProp>
                  <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                  <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                  <boolProp name="HTTPSampler.md5">false</boolProp>
                  <intProp name="HTTPSampler.ipSourceType">0</intProp>
                </HTTPSamplerProxy>
                <hashTree/>
              </hashTree>
            </hashTree>
          </hashTree>
        </hashTree>
        <RandomOrderController guiclass="RandomOrderControllerGui" testclass="RandomOrderController" testname="Random Order Controller (Other Screen)" enabled="true"/>
        <hashTree>
          <ThroughputController guiclass="ThroughputControllerGui" testclass="ThroughputController" testname="Throughput Controller (30%)" enabled="true">
            <intProp name="ThroughputController.style">1</intProp>
            <boolProp name="ThroughputController.perThread">false</boolProp>
            <intProp name="ThroughputController.maxThroughput">1</intProp>
            <FloatProperty>
              <name>ThroughputController.percentThroughput</name>
              <value>30.0</value>
              <savedValue>0.0</savedValue>
            </FloatProperty>
          </ThroughputController>
          <hashTree>
            <GenericController guiclass="LogicControllerGui" testclass="GenericController" testname="Simple Controller (Location Screen)" enabled="true"/>
            <hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List circles" enabled="true">
                <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="offset" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.name">offset</stringProp>
                      <stringProp name="Argument.value">0</stringProp>
                      <stringProp name="HTTPArgument.content_type">text/plain</stringProp>
                    </elementProp>
                    <elementProp name="max" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.name">max</stringProp>
                      <stringProp name="Argument.value">10</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain">${host}</stringProp>
                <stringProp name="HTTPSampler.port">${port}</stringProp>
                <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                <stringProp name="HTTPSampler.path">/api/circles</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                <boolProp name="HTTPSampler.image_parser">false</boolProp>
                <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                <boolProp name="HTTPSampler.md5">false</boolProp>
                <intProp name="HTTPSampler.ipSourceType">0</intProp>
              </HTTPSamplerProxy>
              <hashTree>
                <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON Extractor (Get first circle ID)" enabled="true">
                  <stringProp name="JSONPostProcessor.referenceNames">circleId</stringProp>
                  <stringProp name="JSONPostProcessor.jsonPathExprs">$.data[0].id</stringProp>
                  <stringProp name="JSONPostProcessor.match_numbers">1</stringProp>
                </JSONPostProcessor>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Get circle detail" enabled="true">
                <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.value">10</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain">${host}</stringProp>
                <stringProp name="HTTPSampler.port">${port}</stringProp>
                <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                <stringProp name="HTTPSampler.path">/api/circles/${circleId}</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                <boolProp name="HTTPSampler.image_parser">false</boolProp>
                <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                <boolProp name="HTTPSampler.md5">false</boolProp>
                <intProp name="HTTPSampler.ipSourceType">0</intProp>
              </HTTPSamplerProxy>
              <hashTree/>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List circle location coordinates" enabled="true">
                <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.value">10</stringProp>
                    </elementProp>
                    <elementProp name="circleId" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.name">circleId</stringProp>
                      <stringProp name="Argument.value">${circleId}</stringProp>
                    </elementProp>
                    <elementProp name="distance" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.name">distance</stringProp>
                      <stringProp name="Argument.value">10000</stringProp>
                    </elementProp>
                    <elementProp name="latitude" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.name">latitude</stringProp>
                      <stringProp name="Argument.value">14.420567</stringProp>
                    </elementProp>
                    <elementProp name="longitude" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.name">longitude</stringProp>
                      <stringProp name="Argument.value">104.787595</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain">${host}</stringProp>
                <stringProp name="HTTPSampler.port">${port}</stringProp>
                <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                <stringProp name="HTTPSampler.path">/api/locationCoordinates</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                <boolProp name="HTTPSampler.image_parser">false</boolProp>
                <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                <boolProp name="HTTPSampler.md5">false</boolProp>
                <intProp name="HTTPSampler.ipSourceType">0</intProp>
              </HTTPSamplerProxy>
              <hashTree/>
            </hashTree>
          </hashTree>
          <ThroughputController guiclass="ThroughputControllerGui" testclass="ThroughputController" testname="Throughput Controller (20%)" enabled="true">
            <intProp name="ThroughputController.style">1</intProp>
            <boolProp name="ThroughputController.perThread">false</boolProp>
            <intProp name="ThroughputController.maxThroughput">1</intProp>
            <FloatProperty>
              <name>ThroughputController.percentThroughput</name>
              <value>20.0</value>
              <savedValue>0.0</savedValue>
            </FloatProperty>
          </ThroughputController>
          <hashTree>
            <GenericController guiclass="LogicControllerGui" testclass="GenericController" testname="Simple Controller (Browse Screen)" enabled="true"/>
            <hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List campaigns" enabled="true">
                <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="offset" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.value">0</stringProp>
                      <stringProp name="Argument.name">offset</stringProp>
                    </elementProp>
                    <elementProp name="max" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.name">max</stringProp>
                      <stringProp name="Argument.value">10</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain">${host}</stringProp>
                <stringProp name="HTTPSampler.port">${port}</stringProp>
                <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                <stringProp name="HTTPSampler.path">/api/campaigns</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                <boolProp name="HTTPSampler.image_parser">false</boolProp>
                <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                <boolProp name="HTTPSampler.md5">false</boolProp>
                <intProp name="HTTPSampler.ipSourceType">0</intProp>
              </HTTPSamplerProxy>
              <hashTree>
                <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON Extractor (Get first campaign ID)" enabled="true">
                  <stringProp name="JSONPostProcessor.referenceNames">campaignId</stringProp>
                  <stringProp name="JSONPostProcessor.jsonPathExprs">$.data[*].id</stringProp>
                  <stringProp name="JSONPostProcessor.match_numbers">0</stringProp>
                  <stringProp name="JSONPostProcessor.defaultValues">0</stringProp>
                </JSONPostProcessor>
                <hashTree/>
              </hashTree>
              <RandomController guiclass="RandomControlGui" testclass="RandomController" testname="Random Controller" enabled="true">
                <intProp name="InterleaveControl.style">1</intProp>
              </RandomController>
              <hashTree>
                <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Get campaign detail" enabled="true">
                  <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
                  <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                    <collectionProp name="Arguments.arguments"/>
                  </elementProp>
                  <stringProp name="HTTPSampler.domain">${host}</stringProp>
                  <stringProp name="HTTPSampler.port">${port}</stringProp>
                  <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                  <stringProp name="HTTPSampler.path">/api/campaigns/${campaignId}</stringProp>
                  <stringProp name="HTTPSampler.method">GET</stringProp>
                  <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                  <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                  <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                  <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                  <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                  <boolProp name="HTTPSampler.image_parser">false</boolProp>
                  <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                  <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                  <boolProp name="HTTPSampler.md5">false</boolProp>
                  <intProp name="HTTPSampler.ipSourceType">0</intProp>
                </HTTPSamplerProxy>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List donations" enabled="true">
                <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="offset" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.value">0</stringProp>
                      <stringProp name="Argument.name">offset</stringProp>
                    </elementProp>
                    <elementProp name="max" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.name">max</stringProp>
                      <stringProp name="Argument.value">10</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain">${host}</stringProp>
                <stringProp name="HTTPSampler.port">${port}</stringProp>
                <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                <stringProp name="HTTPSampler.path">/api/donations</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                <boolProp name="HTTPSampler.image_parser">false</boolProp>
                <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                <boolProp name="HTTPSampler.md5">false</boolProp>
                <intProp name="HTTPSampler.ipSourceType">0</intProp>
              </HTTPSamplerProxy>
              <hashTree>
                <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON Extractor (Get first donation ID)" enabled="true">
                  <stringProp name="JSONPostProcessor.referenceNames">donationId</stringProp>
                  <stringProp name="JSONPostProcessor.jsonPathExprs">$.data[*].id</stringProp>
                  <stringProp name="JSONPostProcessor.match_numbers">0</stringProp>
                  <stringProp name="JSONPostProcessor.defaultValues">0</stringProp>
                </JSONPostProcessor>
                <hashTree/>
              </hashTree>
              <RandomController guiclass="RandomControlGui" testclass="RandomController" testname="Random Controller" enabled="true">
                <intProp name="InterleaveControl.style">1</intProp>
              </RandomController>
              <hashTree>
                <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Get donation detail" enabled="true">
                  <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
                  <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                    <collectionProp name="Arguments.arguments"/>
                  </elementProp>
                  <stringProp name="HTTPSampler.domain">${host}</stringProp>
                  <stringProp name="HTTPSampler.port">${port}</stringProp>
                  <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                  <stringProp name="HTTPSampler.path">/api/donations/${donationId}</stringProp>
                  <stringProp name="HTTPSampler.method">GET</stringProp>
                  <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                  <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                  <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                  <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                  <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                  <boolProp name="HTTPSampler.image_parser">false</boolProp>
                  <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                  <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                  <boolProp name="HTTPSampler.md5">false</boolProp>
                  <intProp name="HTTPSampler.ipSourceType">0</intProp>
                </HTTPSamplerProxy>
                <hashTree/>
              </hashTree>
            </hashTree>
          </hashTree>
          <ThroughputController guiclass="ThroughputControllerGui" testclass="ThroughputController" testname="Throughput Controller (10%)" enabled="true">
            <intProp name="ThroughputController.style">1</intProp>
            <boolProp name="ThroughputController.perThread">false</boolProp>
            <intProp name="ThroughputController.maxThroughput">1</intProp>
            <FloatProperty>
              <name>ThroughputController.percentThroughput</name>
              <value>10.0</value>
              <savedValue>0.0</savedValue>
            </FloatProperty>
          </ThroughputController>
          <hashTree>
            <GenericController guiclass="LogicControllerGui" testclass="GenericController" testname="Simple Controller (Create Circle Screen)" enabled="true"/>
            <hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List category" enabled="true">
                <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.value">10</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain">${host}</stringProp>
                <stringProp name="HTTPSampler.port">${port}</stringProp>
                <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                <stringProp name="HTTPSampler.path">/api/categories</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                <boolProp name="HTTPSampler.image_parser">false</boolProp>
                <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                <boolProp name="HTTPSampler.md5">false</boolProp>
                <intProp name="HTTPSampler.ipSourceType">0</intProp>
              </HTTPSamplerProxy>
              <hashTree>
                <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON Extractor (Get category&apos;s name)" enabled="true">
                  <stringProp name="JSONPostProcessor.referenceNames">categoryName</stringProp>
                  <stringProp name="JSONPostProcessor.jsonPathExprs">$.data[0].name</stringProp>
                  <stringProp name="JSONPostProcessor.match_numbers">1</stringProp>
                  <stringProp name="JSONPostProcessor.defaultValues">Tree</stringProp>
                </JSONPostProcessor>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="List user accounts" enabled="true">
                <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="offset" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.value">0</stringProp>
                      <stringProp name="Argument.name">offset</stringProp>
                    </elementProp>
                    <elementProp name="max" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.name">max</stringProp>
                      <stringProp name="Argument.value">20</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain">${host}</stringProp>
                <stringProp name="HTTPSampler.port">${port}</stringProp>
                <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                <stringProp name="HTTPSampler.path">/api/userAccounts</stringProp>
                <stringProp name="HTTPSampler.method">GET</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                <boolProp name="HTTPSampler.image_parser">false</boolProp>
                <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                <boolProp name="HTTPSampler.md5">false</boolProp>
                <intProp name="HTTPSampler.ipSourceType">0</intProp>
              </HTTPSamplerProxy>
              <hashTree>
                <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON Extractor (Get user account ID)" enabled="true">
                  <stringProp name="JSONPostProcessor.referenceNames">userAccountIds</stringProp>
                  <stringProp name="JSONPostProcessor.jsonPathExprs">$.data[*].id</stringProp>
                  <stringProp name="JSONPostProcessor.match_numbers">-1</stringProp>
                </JSONPostProcessor>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Upload circle logo" enabled="false">
                <elementProp name="HTTPsampler.Files" elementType="HTTPFileArgs">
                  <collectionProp name="HTTPFileArgs.files">
                    <elementProp name="C:\Users\<USER>\Desktop\Image\chakra_app_test.jpg" elementType="HTTPFileArg">
                      <stringProp name="File.mimetype">image/jpeg</stringProp>
                      <stringProp name="File.paramname">fileUpload</stringProp>
                      <stringProp name="File.path">C:\Users\<USER>\Desktop\Image\chakra_app_test.jpg</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <boolProp name="HTTPSampler.postBodyRaw">false</boolProp>
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments" guiclass="HTTPArgumentsPanel" testclass="Arguments" testname="User Defined Variables" enabled="true">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="uploadType" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <boolProp name="HTTPArgument.use_equals">true</boolProp>
                      <stringProp name="Argument.value">file-upload-type-general</stringProp>
                      <stringProp name="Argument.name">uploadType</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain">${host}</stringProp>
                <stringProp name="HTTPSampler.port">${port}</stringProp>
                <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                <stringProp name="HTTPSampler.path">/dynamicFileupload</stringProp>
                <stringProp name="HTTPSampler.method">POST</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">true</boolProp>
                <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                <boolProp name="HTTPSampler.image_parser">false</boolProp>
                <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                <boolProp name="HTTPSampler.md5">false</boolProp>
                <intProp name="HTTPSampler.ipSourceType">0</intProp>
              </HTTPSamplerProxy>
              <hashTree>
                <JSONPostProcessor guiclass="JSONPostProcessorGui" testclass="JSONPostProcessor" testname="JSON Extractor (Get logo url)" enabled="true">
                  <stringProp name="JSONPostProcessor.referenceNames">logoUrl</stringProp>
                  <stringProp name="JSONPostProcessor.jsonPathExprs">$.data.fileUrl</stringProp>
                  <stringProp name="JSONPostProcessor.match_numbers">1</stringProp>
                </JSONPostProcessor>
                <hashTree/>
              </hashTree>
              <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy" testname="Create circle" enabled="true">
                <boolProp name="HTTPSampler.postBodyRaw">true</boolProp>
                <elementProp name="HTTPsampler.Arguments" elementType="Arguments">
                  <collectionProp name="Arguments.arguments">
                    <elementProp name="" elementType="HTTPArgument">
                      <boolProp name="HTTPArgument.always_encode">false</boolProp>
                      <stringProp name="Argument.value">{&#xd;
    &quot;name&quot;: &quot;Ecoinsoft&quot;,&#xd;
    &quot;description&quot;: &quot;Ecoinsoft&quot;,&#xd;
    &quot;type&quot;: &quot;${categoryName}&quot;,&#xd;
    &quot;logoUrl&quot;: &quot;${logoUrl}&quot;,&#xd;
    &quot;backgroundUrl&quot;: &quot;&quot;,&#xd;
    &quot;members&quot;: [&#xd;
        {&#xd;
            &quot;userId&quot;: ${userAccountIds_1}&#xd;
        },&#xd;
        {&#xd;
            &quot;userId&quot;: ${userAccountIds_2}&#xd;
        }&#xd;
    ]&#xd;
}</stringProp>
                      <stringProp name="Argument.metadata">=</stringProp>
                    </elementProp>
                  </collectionProp>
                </elementProp>
                <stringProp name="HTTPSampler.domain">${host}</stringProp>
                <stringProp name="HTTPSampler.port">${port}</stringProp>
                <stringProp name="HTTPSampler.protocol">${protocol}</stringProp>
                <stringProp name="HTTPSampler.path">api/circles</stringProp>
                <stringProp name="HTTPSampler.method">POST</stringProp>
                <boolProp name="HTTPSampler.follow_redirects">true</boolProp>
                <boolProp name="HTTPSampler.auto_redirects">false</boolProp>
                <boolProp name="HTTPSampler.use_keepalive">true</boolProp>
                <boolProp name="HTTPSampler.DO_MULTIPART_POST">false</boolProp>
                <boolProp name="HTTPSampler.BROWSER_COMPATIBLE_MULTIPART">false</boolProp>
                <boolProp name="HTTPSampler.image_parser">false</boolProp>
                <boolProp name="HTTPSampler.concurrentDwn">false</boolProp>
                <stringProp name="HTTPSampler.concurrentPool">6</stringProp>
                <boolProp name="HTTPSampler.md5">false</boolProp>
                <intProp name="HTTPSampler.ipSourceType">0</intProp>
              </HTTPSamplerProxy>
              <hashTree/>
            </hashTree>
          </hashTree>
        </hashTree>
        <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="TableVisualizer" testclass="ResultCollector" testname="View Results in Table" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="Summary Report" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="RespTimeGraphVisualizer" testclass="ResultCollector" testname="Response Time Graph" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
          <intProp name="RespTimeGraph.legendsize">5</intProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="GraphVisualizer" testclass="ResultCollector" testname="Graph Results" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="StatGraphVisualizer" testclass="ResultCollector" testname="Aggregate Graph" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename"></stringProp>
        </ResultCollector>
        <hashTree/>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
