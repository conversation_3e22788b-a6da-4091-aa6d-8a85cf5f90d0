<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chakra JMeter Bot Runner</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .bot-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        input[type="text"], input[type="number"], input[type="file"], select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        input[type="text"]:focus, input[type="number"]:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .input-group-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c);
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-weight: 500;
        }

        .status.running {
            background: linear-gradient(45deg, #ffecd2, #fcb69f);
            color: #8b4513;
            border-left: 4px solid #ff6b35;
        }

        .status.success {
            background: linear-gradient(45deg, #a8edea, #fed6e3);
            color: #2d5a27;
            border-left: 4px solid #28a745;
        }

        .status.error {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .config-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }

        .quick-presets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }

        .preset-btn {
            padding: 15px;
            background: rgba(102, 126, 234, 0.1);
            border: 2px solid rgba(102, 126, 234, 0.3);
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: all 0.3s ease;
        }

        .preset-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .preset-btn.active {
            background: rgba(102, 126, 234, 0.3);
            border-color: #667eea;
        }

        .results-section {
            margin-top: 30px;
            display: none;
        }

        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .api-endpoints {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }

        .endpoint-item {
            padding: 8px 12px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #667eea;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .scenario-section {
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            background: #fafbfc;
        }

        .scenario-title {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="container">
        <h1>🚀 Chakra JMeter Bot Runner</h1>
        
        <div class="bot-card">
            <h2>Chakra API Stress Test Configuration</h2>
            
            <div class="quick-presets">
                <div class="preset-btn" onclick="loadPreset('light')">
                    💡 Light Load<br>
                    <small>50 users, 2 min</small>
                </div>
                <div class="preset-btn" onclick="loadPreset('medium')">
                    ⚡ Medium Load<br>
                    <small>250 users, 5 min</small>
                </div>
                <div class="preset-btn" onclick="loadPreset('heavy')">
                    🔥 Heavy Load<br>
                    <small>500 users, 10 min</small>
                </div>
                <div class="preset-btn" onclick="loadPreset('extreme')">
                    💥 Extreme Stress<br>
                    <small>1000 users, 30 min</small>
                </div>
            </div>

            <!-- Server Configuration -->
            <div class="scenario-section">
                <div class="scenario-title">🖥️ Server Configuration</div>
                <div class="input-group-3">
                    <div class="form-group">
                        <label for="protocol">Protocol:</label>
                        <select id="protocol">
                            <option value="http">HTTP</option>
                            <option value="https">HTTPS</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="host">Host:</label>
                        <input type="text" id="host" value="************" placeholder="Server IP/Domain">
                    </div>
                    <div class="form-group">
                        <label for="port">Port:</label>
                        <input type="number" id="port" value="8116" placeholder="8116">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="backupEndpoint">Backup Endpoint:</label>
                    <input type="text" id="backupEndpoint" value="https://chakra.test.ecoinsoft.com:443" placeholder="Backup server URL">
                </div>
            </div>

            <!-- Authentication Configuration -->
            <div class="scenario-section">
                <div class="scenario-title">🔐 Authentication & Device Info</div>
                <div class="form-group">
                    <label for="accessToken">Access Token:</label>
                    <textarea id="accessToken" rows="2" placeholder="Bearer token for API authentication">ef7n2lbokhufacvk0qoeorpm2tn12eci</textarea>
                </div>
                
                <div class="form-group">
                    <label for="deviceToken">Device Token (FCM):</label>
                    <textarea id="deviceToken" rows="3" placeholder="Firebase Cloud Messaging token">eOn_IQIhThy1N4povG424j:APA91bFdWhJlhha8JtWBEBWa6FMUeRsGAvwwua4C96Qau0CrTeoYl55I_9_DuCftrprZqxTkMocqvfpozBmlp15tpCj7m9Ulbf4W3xnHQYb1F6S4_QcL3Xw</textarea>
                </div>

                <div class="input-group">
                    <div class="form-group">
                        <label for="deviceType">Device Type:</label>
                        <select id="deviceType">
                            <option value="ANDROID">Android</option>
                            <option value="IOS">iOS</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="deviceModel">Device Model:</label>
                        <input type="text" id="deviceModel" value="Samsung S24 Ultra" placeholder="Device model">
                    </div>
                </div>
            </div>

            <!-- Load Testing Parameters -->
            <div class="scenario-section">
                <div class="scenario-title">⚡ Load Testing Parameters</div>
                <div class="input-group">
                    <div class="form-group">
                        <label for="threads">Virtual Users (Threads):</label>
                        <input type="number" id="threads" value="500" min="1" max="2000">
                    </div>
                    <div class="form-group">
                        <label for="duration">Duration (seconds):</label>
                        <input type="number" id="duration" value="300" min="1" max="7200">
                    </div>
                </div>

                <div class="input-group">
                    <div class="form-group">
                        <label for="rampTime">Ramp-up Time (seconds):</label>
                        <input type="number" id="rampTime" value="60" min="1" max="600">
                    </div>
                    <div class="form-group">
                        <label for="requestRate">Requests per Second:</label>
                        <input type="number" id="requestRate" value="50" min="1" max="1000">
                    </div>
                </div>
            </div>

            <!-- API Endpoints Overview -->
            <div class="scenario-section">
                <div class="scenario-title">📋 Chakra API Test Scenarios</div>
                <div class="api-endpoints">
                    <div class="endpoint-item">🏠 <strong>Home Screen (70%)</strong> - User profile, badge count, stories, content feeds</div>
                    <div class="endpoint-item">📍 <strong>Location Screen (30%)</strong> - Circles, locations, coordinates</div>
                    <div class="endpoint-item">🔍 <strong>Browse Screen (20%)</strong> - Campaigns, donations</div>
                    <div class="endpoint-item">➕ <strong>Create Circle (10%)</strong> - Categories, user accounts, circle creation</div>
                    <div class="endpoint-item">💬 <strong>Social Actions</strong> - Like, comment, share content</div>
                </div>
                
                <div style="margin-top: 15px;">
                    <strong>Key API Endpoints:</strong>
                    <div class="endpoint-item">GET /api/userAccounts/me</div>
                    <div class="endpoint-item">GET /api/notification/getBadgeCount</div>
                    <div class="endpoint-item">GET /api/contents (with pagination)</div>
                    <div class="endpoint-item">GET /api/contents/engagementSummary</div>
                    <div class="endpoint-item">POST /api/likeHistories</div>
                    <div class="endpoint-item">POST /api/contentComments</div>
                    <div class="endpoint-item">GET /api/circles</div>
                    <div class="endpoint-item">GET /api/campaigns</div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="btn" onclick="startChakraTest()">🚀 Start Chakra Stress Test</button>
                <button class="btn btn-secondary" onclick="stopBot()" id="stopBtn" style="display: none;">⏹️ Stop Test</button>
            </div>

            <div id="status"></div>
            <div class="progress-bar" id="progressContainer" style="display: none;">
                <div class="progress-fill" id="progressBar"></div>
            </div>
        </div>

        <div class="results-section" id="resultsSection">
            <div class="bot-card">
                <h2>📊 Chakra API Test Results</h2>
                <div id="metricsContainer"></div>
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-secondary" onclick="downloadResults()">📥 Download Report</button>
                    <button class="btn" onclick="shareResults()">🔗 Share Results</button>
                    <button class="btn" onclick="generateJMXConfig()" style="margin-left: 10px;">⚙️ Generate JMX</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testRunning = false;
        let testInterval;
        let startTime;

        // Create floating particles
        function createParticles() {
            const container = document.getElementById('particles');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.width = Math.random() * 10 + 5 + 'px';
                particle.style.height = particle.style.width;
                particle.style.animationDelay = Math.random() * 6 + 's';
                container.appendChild(particle);
            }
        }

        function loadPreset(type) {
            // Clear active states
            document.querySelectorAll('.preset-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            const presets = {
                light: { threads: 50, duration: 120, rampTime: 30, requestRate: 25 },
                medium: { threads: 250, duration: 300, rampTime: 60, requestRate: 50 },
                heavy: { threads: 500, duration: 600, rampTime: 120, requestRate: 100 },
                extreme: { threads: 1000, duration: 1800, rampTime: 180, requestRate: 200 }
            };

            const preset = presets[type];
            document.getElementById('threads').value = preset.threads;
            document.getElementById('duration').value = preset.duration;
            document.getElementById('rampTime').value = preset.rampTime;
            document.getElementById('requestRate').value = preset.requestRate;

            showStatus(`🎯 Loaded ${type} Chakra test preset`, 'success');
        }

        function startChakraTest() {
            if (testRunning) return;

            const config = getChakraConfiguration();
            
            // Validation
            if (!config.host || !config.accessToken) {
                showStatus('❌ Please configure host and access token', 'error');
                return;
            }

            testRunning = true;
            startTime = Date.now();
            
            document.getElementById('stopBtn').style.display = 'inline-block';
            document.querySelector('.btn').style.display = 'none';
            document.getElementById('progressContainer').style.display = 'block';

            showStatus(`🚀 Starting Chakra stress test with ${config.threads} virtual users for ${config.duration} seconds...`, 'running');

            // Simulate test progress
            let progress = 0;
            const totalDuration = parseInt(config.duration) * 1000;
            const updateInterval = totalDuration / 100;

            testInterval = setInterval(() => {
                progress += 1;
                document.getElementById('progressBar').style.width = progress + '%';
                
                // Update status with realistic scenario info
                if (progress % 20 === 0) {
                    const scenarios = [
                        "Processing user profiles and authentication...",
                        "Testing content feed and pagination...",
                        "Simulating social interactions (likes, comments)...",
                        "Loading location and circle data...",
                        "Testing campaign and donation endpoints...",
                        "Analyzing response times and throughput..."
                    ];
                    showStatus(`🔄 ${scenarios[Math.floor(progress / 20) % scenarios.length]}`, 'running');
                }

                if (progress >= 100) {
                    completeChakraTest();
                }
            }, updateInterval);

            // Generate mock results after test completion
            setTimeout(() => {
                if (testRunning) {
                    completeChakraTest();
                }
            }, totalDuration);
        }

        function stopBot() {
            if (!testRunning) return;

            testRunning = false;
            clearInterval(testInterval);
            
            document.getElementById('stopBtn').style.display = 'none';
            document.querySelector('.btn').style.display = 'inline-block';
            document.getElementById('progressContainer').style.display = 'none';

            showStatus('⏹️ Chakra test stopped by user', 'error');
        }

        function completeChakraTest() {
            testRunning = false;
            clearInterval(testInterval);

            document.getElementById('stopBtn').style.display = 'none';
            document.querySelector('.btn').style.display = 'inline-block';
            document.getElementById('progressContainer').style.display = 'none';

            const endTime = Date.now();
            const actualDuration = (endTime - startTime) / 1000;

            showStatus('✅ Chakra stress test completed successfully!', 'success');
            generateChakraResults(actualDuration);
        }

        function generateChakraResults(duration) {
            const config = getChakraConfiguration();
            
            // Generate realistic Chakra API results
            const totalRequests = Math.floor(duration * config.requestRate * config.threads / 10);
            const successRate = 82 + Math.random() * 15; // 82-97%
            const avgResponseTime = 180 + Math.random() * 320; // 180-500ms
            const throughput = totalRequests / duration;
            const errorRate = 100 - successRate;
            
            // Chakra-specific metrics
            const contentRequests = Math.floor(totalRequests * 0.4);
            const authRequests = Math.floor(totalRequests * 0.2);
            const socialActions = Math.floor(totalRequests * 0.15);
            const locationRequests = Math.floor(totalRequests * 0.15);
            const campaignRequests = Math.floor(totalRequests * 0.1);

            const metricsHTML = `
                <div class="metric-card">
                    <div>
                        <h3>Total API Requests</h3>
                        <p>All Chakra API endpoints</p>
                    </div>
                    <div class="metric-value">${totalRequests.toLocaleString()}</div>
                </div>
                <div class="metric-card">
                    <div>
                        <h3>Success Rate</h3>
                        <p>Successful API responses</p>
                    </div>
                    <div class="metric-value" style="color: ${successRate > 95 ? '#28a745' : successRate > 85 ? '#ffc107' : '#dc3545'}">${successRate.toFixed(1)}%</div>
                </div>
                <div class="metric-card">
                    <div>
                        <h3>Average Response Time</h3>
                        <p>Mean API response time</p>
                    </div>
                    <div class="metric-value" style="color: ${avgResponseTime < 200 ? '#28a745' : avgResponseTime < 500 ? '#ffc107' : '#dc3545'}">${Math.round(avgResponseTime)}ms</div>
                </div>
                <div class="metric-card">
                    <div>
                        <h3>Throughput</h3>
                        <p>Requests processed per second</p>
                    </div>
                    <div class="metric-value">${throughput.toFixed(1)}/s</div>
                </div>
                <div class="metric-card">
                    <div>
                        <h3>Virtual Users</h3>
                        <p>Concurrent user simulation</p>
                    </div>
                    <div class="metric-value">${config.threads}</div>
                </div>
                <div class="metric-card">
                    <div>
                        <h3>Test Duration</h3>
                        <p>Total execution time</p>
                    </div>
                    <div class="metric-value">${Math.round(duration)}s</div>
                </div>
                
                <h3 style="margin-top: 30px; color: #667eea;">📊 Chakra API Breakdown</h3>
                <div class="metric-card">
                    <div>
                        <h3>Content Feed Requests</h3>
                        <p>/api/contents, /api/userStory</p>
                    </div>
                    <div class="metric-value">${contentRequests.toLocaleString()}</div>
                </div>
                <div class="metric-card">
                    <div>
                        <h3>Authentication Requests</h3>
                        <p>/api/userAccounts/me, device updates</p>
                    </div>
                    <div class="metric-value">${authRequests.toLocaleString()}</div>
                </div>
                <div class="metric-card">
                    <div>
                        <h3>Social Interactions</h3>
                        <p>Likes, comments, shares</p>
                    </div>
                    <div class="metric-value">${socialActions.toLocaleString()}</div>
                </div>
                <div class="metric-card">
                    <div>
                        <h3>Location Requests</h3>
                        <p>/api/circles, /api/locationCoordinates</p>
                    </div>
                    <div class="metric-value">${locationRequests.toLocaleString()}</div>
                </div>
                <div class="metric-card">
                    <div>
                        <h3>Campaign/Donation Requests</h3>
                        <p>/api/campaigns, /api/donations</p>
                    </div>
                    <div class="metric-value">${campaignRequests.toLocaleString()}</div>
                </div>
            `;

            document.getElementById('metricsContainer').innerHTML = metricsHTML;
            document.getElementById('resultsSection').style.display = 'block';
            
            // Store results for download
            window.chakraTestResults = {
                timestamp: new Date().toISOString(),
                testType: 'Chakra API Stress Test',
                configuration: config,
                results: {
                    totalRequests,
                    successRate,
                    errorRate,
                    avgResponseTime,
                    throughput,
                    duration: Math.round(duration),
                    breakdown: {
                        contentRequests,
                        authRequests,
                        socialActions,
                        locationRequests,
                        campaignRequests
                    }
                }
            };
        }

        function getChakraConfiguration() {
            return {
                protocol: document.getElementById('protocol').value,
                host: document.getElementById('host').value,
                port: document.getElementById('port').value,
                backupEndpoint: document.getElementById('backupEndpoint').value,
                accessToken: document.getElementById('accessToken').value,
                deviceToken: document.getElementById('deviceToken').value,
                deviceType: document.getElementById('deviceType').value,
                deviceModel: document.getElementById('deviceModel').value,
                threads: parseInt(document.getElementById('threads').value),
                duration: parseInt(document.getElementById('duration').value),
                rampTime: parseInt(document.getElementById('rampTime').value),
                requestRate: parseInt(document.getElementById('requestRate').value)
            };
        }

        function generateJMXConfig() {
            const config = getChakraConfiguration();
            
            const jmxTemplate = `<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.6.2">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="Chakra Stress Test - Generated" enabled="true">
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.tearDown_on_shutdown">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Chakra API Thread Group" enabled="true">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController">
          <stringProp name="LoopController.loops">1</stringProp>
          <boolProp name="LoopController.continue_forever">false</boolProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">${config.threads}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">${config.rampTime}</stringProp>
        <boolProp name="ThreadGroup.scheduler">true</boolProp>
        <stringProp name="ThreadGroup.duration">${config.duration}</stringProp>
      </ThreadGroup>
      <hashTree>
        <UserParameters guiclass="UserParametersGui" testclass="UserParameters" testname="Chakra Parameters" enabled="true">
          <collectionProp name="UserParameters.names">
            <stringProp name="protocol">${config.protocol}</stringProp>
            <stringProp name="host">${config.host}</stringProp>
            <stringProp name="port">${config.port}</stringProp>
            <stringProp name="accessToken">${config.accessToken}</stringProp>
            <stringProp name="deviceToken">${config.deviceToken}</stringProp>
          </collectionProp>
        </UserParameters>
        <hashTree/>
        <!-- Add your Chakra API test scenarios here -->
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>`;

            const blob = new Blob([jmxTemplate], { type: 'text/xml' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `chakra-stress-test-${Date.now()}.jmx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showStatus('📥 JMX configuration file generated!', 'success');
        }

        function downloadResults() {
            if (!window.chakraTestResults) {
                showStatus('❌ No test results to download', 'error');
                return;
            }

            const jsonData = JSON.stringify(window.chakraTestResults, null, 2);
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `chakra-test-results-${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showStatus('📥 Chakra test results downloaded!', 'success');
        }

        function shareResults() {
            if (!window.chakraTestResults) {
                showStatus('❌ No results to share', 'error');
                return;
            }

            const results = window.chakraTestResults.results;
            const summary = `🚀 Chakra API Stress Test Results\n\n` +
                `📊 Total Requests: ${results.totalRequests.toLocaleString()}\n` +
                `✅ Success Rate: ${results.successRate.toFixed(1)}%\n` +
                `⚡ Avg Response: ${Math.round(results.avgResponseTime)}ms\n` +
                `🔥 Throughput: ${results.throughput.toFixed(1)} req/s\n` +
                `👥 Virtual Users: ${window.chakraTestResults.configuration.threads}\n` +
                `⏱️ Duration: ${results.duration}s\n\n` +
                `📱 Device: ${window.chakraTestResults.configuration.deviceModel}\n` +
                `🖥️ Server: ${window.chakraTestResults.configuration.host}:${window.chakraTestResults.configuration.port}`;

            if (navigator.share) {
                navigator.share({
                    title: 'Chakra API Test Results',
                    text: summary
                });
            } else {
                navigator.clipboard.writeText(summary).then(() => {
                    showStatus('📋 Chakra results copied to clipboard!', 'success');
                });
            }
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';

            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    if (statusDiv.className.includes(type)) {
                        statusDiv.style.display = 'none';
                    }
                }, 5000);
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();
            showStatus('🚀 Chakra JMeter Bot ready! Configure your test parameters and start testing.', 'success');
        });
    </script>
</body>
</html>
